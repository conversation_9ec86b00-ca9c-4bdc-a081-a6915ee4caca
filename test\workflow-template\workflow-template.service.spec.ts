/**
 * Workflow Template Service Unit Tests
 *
 * This file contains comprehensive unit tests for the WorkflowTemplateService.
 * Tests cover all CRUD operations, validation, error handling, and business logic.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import {
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { WorkflowTemplateService } from '../../src/workflow-template/workflow-template.service';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  CreateWorkflowTemplateDto,
  UpdateWorkflowTemplateDto,
} from '../../src/workflow-template/dto/workflow-template.dto';

describe('WorkflowTemplateService', () => {
  let service: WorkflowTemplateService;
  let prismaService: PrismaService;

  // Mock data
  const mockAdminUser = {
    id: 'admin123',
    email: '<EMAIL>',
    tokenType: 'admin',
    sub: { name: 'Admin User' },
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockWorkflowTemplate = {
    id: 'clx1234567890abcdef',
    name: 'Standard Immigration Workflow Template',
    description: 'Complete workflow template for immigration applications',
    serviceType: 'immigration',
    serviceId: 'immigration_service_123',
    isActive: true,
    workflowTemplate: [
      {
        stageName: 'Document Collection',
        stageOrder: 1,
        documentsRequired: true,
        documents: [
          { documentName: 'passport', required: true },
          { documentName: 'visa', required: true },
        ],
        customFormRequired: true,
        customForm: [
          { fieldName: 'name', fieldType: 'text', required: true },
          { fieldName: 'age', fieldType: 'number', required: true },
        ],
      },
      {
        stageName: 'Initial Review',
        stageOrder: 2,
        documentsRequired: false,
        customFormRequired: false,
      },
    ],
    createdBy: 'Admin User',
    updatedBy: null,
    createdAt: new Date('2025-01-06T10:30:00.000Z'),
    updatedAt: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateWorkflowTemplateDto = {
    name: 'Standard Immigration Workflow Template',
    description: 'Complete workflow template for immigration applications',
    serviceType: 'immigration',
    serviceId: 'immigration_service_123',
    isActive: true,
    workflowTemplate: [
      {
        stageName: 'Document Collection',
        stageOrder: 1,
        documentsRequired: true,
        documents: [
          { documentName: 'passport', required: true },
          { documentName: 'visa', required: true },
        ],
        customFormRequired: true,
        customForm: [
          { fieldName: 'name', fieldType: 'text', required: true },
          { fieldName: 'age', fieldType: 'number', required: true },
        ],
      },
      {
        stageName: 'Initial Review',
        stageOrder: 2,
        documentsRequired: false,
        customFormRequired: false,
      },
    ],
  };

  // Mock Prisma service
  const mockPrismaService = {
    workflow_template: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    application: {
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    immigration_service: {
      findUnique: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowTemplateService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WorkflowTemplateService>(WorkflowTemplateService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a workflow template successfully', async () => {
      mockPrismaService.workflow_template.findFirst.mockResolvedValue(null);
      mockPrismaService.workflow_template.create.mockResolvedValue(
        mockWorkflowTemplate,
      );

      const result = await service.create(mockCreateDto, mockAdminUser);

      expect(
        mockPrismaService.workflow_template.findFirst,
      ).toHaveBeenCalledWith({
        where: {
          name: {
            equals: mockCreateDto.name,
            mode: 'insensitive',
          },
        },
      });

      expect(mockPrismaService.workflow_template.create).toHaveBeenCalledWith({
        data: {
          name: mockCreateDto.name,
          description: mockCreateDto.description,
          serviceType: mockCreateDto.serviceType,
          serviceId: mockCreateDto.serviceId,
          isActive: mockCreateDto.isActive,
          workflowTemplate: mockCreateDto.workflowTemplate,
          createdBy: 'Admin User',
        },
      });

      expect(result).toEqual({
        id: mockWorkflowTemplate.id,
        name: mockWorkflowTemplate.name,
        description: mockWorkflowTemplate.description,
        serviceType: mockWorkflowTemplate.serviceType,
        serviceId: mockWorkflowTemplate.serviceId,
        isActive: mockWorkflowTemplate.isActive,
        workflowTemplate: mockWorkflowTemplate.workflowTemplate,
        createdBy: mockWorkflowTemplate.createdBy,
        updatedBy: mockWorkflowTemplate.updatedBy,
        createdAt: mockWorkflowTemplate.createdAt,
        updatedAt: mockWorkflowTemplate.updatedAt,
      });
    });

    it('should throw ConflictException if template name already exists', async () => {
      mockPrismaService.workflow_template.findFirst.mockResolvedValue(
        mockWorkflowTemplate,
      );

      await expect(
        service.create(mockCreateDto, mockAdminUser),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.create(mockCreateDto, mockAdminUser),
      ).rejects.toThrow(
        'Workflow template with name "Standard Immigration Workflow Template" already exists',
      );
    });

    it('should throw BadRequestException for invalid workflow template structure', async () => {
      const invalidDto = {
        ...mockCreateDto,
        workflowTemplate: [], // Empty array
      };

      mockPrismaService.workflow_template.findFirst.mockResolvedValue(null);

      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        'Workflow template must contain at least one stage',
      );
    });

    it('should throw BadRequestException for duplicate stage orders', async () => {
      const invalidDto = {
        ...mockCreateDto,
        workflowTemplate: [
          { stageName: 'Stage 1', stageOrder: 1 },
          { stageName: 'Stage 2', stageOrder: 1 }, // Duplicate order
        ],
      };

      mockPrismaService.workflow_template.findFirst.mockResolvedValue(null);

      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        'Workflow template stages must have unique stage orders',
      );
    });

    it('should throw BadRequestException for non-sequential stage orders', async () => {
      const invalidDto = {
        ...mockCreateDto,
        workflowTemplate: [
          { stageName: 'Stage 1', stageOrder: 1 },
          { stageName: 'Stage 3', stageOrder: 3 }, // Missing order 2
        ],
      };

      mockPrismaService.workflow_template.findFirst.mockResolvedValue(null);

      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.create(invalidDto, mockAdminUser)).rejects.toThrow(
        'Workflow template stage orders must be sequential starting from 1',
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated workflow templates', async () => {
      const mockTemplates = [mockWorkflowTemplate];
      const mockTotal = 1;

      mockPrismaService.workflow_template.findMany.mockResolvedValue(
        mockTemplates,
      );
      mockPrismaService.workflow_template.count.mockResolvedValue(mockTotal);

      const filters = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc' as const,
      };

      const result = await service.findAll(filters);

      expect(result).toEqual({
        data: [
          {
            id: mockWorkflowTemplate.id,
            name: mockWorkflowTemplate.name,
            description: mockWorkflowTemplate.description,
            serviceType: mockWorkflowTemplate.serviceType,
            serviceId: mockWorkflowTemplate.serviceId,
            isActive: mockWorkflowTemplate.isActive,
            workflowTemplate: mockWorkflowTemplate.workflowTemplate,
            createdBy: mockWorkflowTemplate.createdBy,
            updatedBy: mockWorkflowTemplate.updatedBy,
            createdAt: mockWorkflowTemplate.createdAt,
            updatedAt: mockWorkflowTemplate.updatedAt,
          },
        ],
        total: mockTotal,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should apply search filter correctly', async () => {
      mockPrismaService.workflow_template.findMany.mockResolvedValue([]);
      mockPrismaService.workflow_template.count.mockResolvedValue(0);

      const filters = {
        search: 'immigration',
        page: 1,
        limit: 10,
      };

      await service.findAll(filters);

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            OR: [
              {
                name: {
                  contains: 'immigration',
                  mode: 'insensitive',
                },
              },
              {
                description: {
                  contains: 'immigration',
                  mode: 'insensitive',
                },
              },
            ],
          },
          orderBy: { createdAt: 'desc' },
          skip: 0,
          take: 10,
        },
      );
    });
  });

  describe('findOne', () => {
    it('should return a workflow template by ID', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      const result = await service.findOne(mockWorkflowTemplate.id);

      expect(
        mockPrismaService.workflow_template.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: mockWorkflowTemplate.id },
      });

      expect(result).toEqual({
        id: mockWorkflowTemplate.id,
        name: mockWorkflowTemplate.name,
        description: mockWorkflowTemplate.description,
        serviceType: mockWorkflowTemplate.serviceType,
        serviceId: mockWorkflowTemplate.serviceId,
        isActive: mockWorkflowTemplate.isActive,
        workflowTemplate: mockWorkflowTemplate.workflowTemplate,
        createdBy: mockWorkflowTemplate.createdBy,
        updatedBy: mockWorkflowTemplate.updatedBy,
        createdAt: mockWorkflowTemplate.createdAt,
        updatedAt: mockWorkflowTemplate.updatedAt,
      });
    });

    it('should throw NotFoundException if template not found', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        'Workflow template with ID non-existent-id not found',
      );
    });
  });

  describe('update', () => {
    const updateDto: UpdateWorkflowTemplateDto = {
      name: 'Updated Template Name',
      description: 'Updated description',
    };

    it('should update a workflow template successfully', async () => {
      const updatedTemplate = {
        ...mockWorkflowTemplate,
        name: updateDto.name,
        description: updateDto.description,
        updatedBy: 'Admin User',
      };

      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );
      mockPrismaService.workflow_template.findFirst.mockResolvedValue(null);
      mockPrismaService.workflow_template.update.mockResolvedValue(
        updatedTemplate,
      );

      const result = await service.update(
        mockWorkflowTemplate.id,
        updateDto,
        mockAdminUser,
      );

      expect(mockPrismaService.workflow_template.update).toHaveBeenCalledWith({
        where: { id: mockWorkflowTemplate.id },
        data: {
          name: updateDto.name,
          description: updateDto.description,
          updatedBy: 'Admin User',
        },
      });

      expect(result.name).toBe(updateDto.name);
      expect(result.description).toBe(updateDto.description);
    });

    it('should throw NotFoundException if template not found', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(
        service.update('non-existent-id', updateDto, mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if name already exists', async () => {
      const existingTemplate = { ...mockWorkflowTemplate, id: 'different-id' };

      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );
      mockPrismaService.workflow_template.findFirst.mockResolvedValue(
        existingTemplate,
      );

      await expect(
        service.update(mockWorkflowTemplate.id, updateDto, mockAdminUser),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('remove', () => {
    it('should delete a workflow template successfully', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );
      mockPrismaService.application.count.mockResolvedValue(0); // No active applications
      mockPrismaService.application.groupBy.mockResolvedValue([]);
      mockPrismaService.workflow_template.delete.mockResolvedValue(
        mockWorkflowTemplate,
      );

      await service.remove(mockWorkflowTemplate.id, mockAdminUser);

      expect(mockPrismaService.workflow_template.delete).toHaveBeenCalledWith({
        where: { id: mockWorkflowTemplate.id },
      });
    });

    it('should throw NotFoundException if template not found', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(
        service.remove('non-existent-id', mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException if template is in use', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );
      mockPrismaService.application.count.mockResolvedValue(5); // Active applications
      mockPrismaService.application.groupBy.mockResolvedValue([
        { service_type: 'immigration', _count: { id: 10 } },
      ]);

      await expect(
        service.remove(mockWorkflowTemplate.id, mockAdminUser),
      ).rejects.toThrow(ConflictException);
      await expect(
        service.remove(mockWorkflowTemplate.id, mockAdminUser),
      ).rejects.toThrow(
        'Cannot delete workflow template. It is currently in use by 5 active applications.',
      );
    });
  });

  describe('checkUsage', () => {
    it('should return usage information', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );
      mockPrismaService.application.count
        .mockResolvedValueOnce(2) // Active applications
        .mockResolvedValueOnce(5); // Total applications
      mockPrismaService.application.groupBy.mockResolvedValue([
        { service_type: 'immigration', _count: { id: 5 } },
      ]);

      const result = await service.checkUsage(mockWorkflowTemplate.id);

      expect(result).toEqual({
        templateId: mockWorkflowTemplate.id,
        activeApplications: 2,
        totalApplications: 5,
        canDelete: false,
        usageDetails: [{ serviceType: 'immigration', count: 5 }],
      });
    });

    it('should throw NotFoundException if template not found', async () => {
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(service.checkUsage('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findByServiceType', () => {
    it('should return templates by service type', async () => {
      mockPrismaService.workflow_template.findMany.mockResolvedValue([
        mockWorkflowTemplate,
      ]);

      const result = await service.findByServiceType('immigration');

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            serviceType: 'immigration',
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
      );

      expect(result).toHaveLength(1);
      expect(result[0].serviceType).toBe('immigration');
    });
  });

  describe('Service-Based Duplicate Prevention', () => {
    describe('create - duplicate prevention', () => {
      it('should throw ConflictException when creating active template with same serviceType and serviceId', async () => {
        const existingActiveTemplate = {
          id: 'existing-template-id',
          name: 'Existing Immigration Template',
          serviceType: 'immigration',
          serviceId: 'immigration_service_123',
          isActive: true,
        };

        const createDtoWithDifferentName: CreateWorkflowTemplateDto = {
          ...mockCreateDto,
          name: 'Different Template Name', // Use different name to pass name check
        };

        // Mock name check passes (different name), service duplicate check fails
        mockPrismaService.workflow_template.findFirst
          .mockResolvedValueOnce(null) // Name check passes
          .mockResolvedValueOnce(existingActiveTemplate); // Service duplicate check fails

        await expect(
          service.create(createDtoWithDifferentName, mockAdminUser),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.create(createDtoWithDifferentName, mockAdminUser),
        ).rejects.toThrow(
          'An active workflow template already exists for immigration/immigration_service_123',
        );
      });

      it('should throw ConflictException when creating active template with same serviceType and null serviceId', async () => {
        const dtoWithNullServiceId: CreateWorkflowTemplateDto = {
          ...mockCreateDto,
          name: 'Different General Template Name', // Use different name to pass name check
          serviceId: undefined,
        };

        const existingActiveTemplate = {
          id: 'existing-template-id',
          name: 'Existing General Immigration Template',
          serviceType: 'immigration',
          serviceId: null,
          isActive: true,
        };

        // Mock name check passes, service duplicate check fails
        mockPrismaService.workflow_template.findFirst
          .mockResolvedValueOnce(null) // Name check passes
          .mockResolvedValueOnce(existingActiveTemplate); // Service duplicate check fails

        await expect(
          service.create(dtoWithNullServiceId, mockAdminUser),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.create(dtoWithNullServiceId, mockAdminUser),
        ).rejects.toThrow(
          'An active workflow template already exists for immigration',
        );
      });

      it('should allow creating inactive template with same serviceType and serviceId', async () => {
        const inactiveDto: CreateWorkflowTemplateDto = {
          ...mockCreateDto,
          isActive: false,
        };

        const existingActiveTemplate = {
          id: 'existing-template-id',
          name: 'Existing Immigration Template',
          serviceType: 'immigration',
          serviceId: 'immigration_service_123',
          isActive: true,
        };

        // Mock name check passes, no service duplicate check for inactive templates
        mockPrismaService.workflow_template.findFirst.mockResolvedValueOnce(
          null,
        );
        mockPrismaService.workflow_template.create.mockResolvedValue({
          ...mockWorkflowTemplate,
          isActive: false,
        });

        const result = await service.create(inactiveDto, mockAdminUser);

        expect(result.isActive).toBe(false);
        // Service duplicate check should not be called for inactive templates
        expect(
          mockPrismaService.workflow_template.findFirst,
        ).toHaveBeenCalledTimes(1);
      });

      it('should allow creating active template with different serviceType', async () => {
        const differentServiceTypeDto: CreateWorkflowTemplateDto = {
          ...mockCreateDto,
          serviceType: 'training',
        };

        // Mock name check passes, service duplicate check passes (different service type)
        mockPrismaService.workflow_template.findFirst
          .mockResolvedValueOnce(null) // Name check
          .mockResolvedValueOnce(null); // Service duplicate check
        mockPrismaService.workflow_template.create.mockResolvedValue({
          ...mockWorkflowTemplate,
          serviceType: 'training',
        });

        const result = await service.create(
          differentServiceTypeDto,
          mockAdminUser,
        );

        expect(result.serviceType).toBe('training');
      });

      it('should allow creating active template with different serviceId', async () => {
        const differentServiceIdDto: CreateWorkflowTemplateDto = {
          ...mockCreateDto,
          serviceId: 'different_service_456',
        };

        // Mock name check passes, service duplicate check passes (different service ID)
        mockPrismaService.workflow_template.findFirst
          .mockResolvedValueOnce(null) // Name check
          .mockResolvedValueOnce(null); // Service duplicate check
        mockPrismaService.workflow_template.create.mockResolvedValue({
          ...mockWorkflowTemplate,
          serviceId: 'different_service_456',
        });

        const result = await service.create(
          differentServiceIdDto,
          mockAdminUser,
        );

        expect(result.serviceId).toBe('different_service_456');
      });
    });

    describe('update - duplicate prevention', () => {
      const existingTemplate = {
        ...mockWorkflowTemplate,
        serviceType: 'immigration',
        serviceId: 'immigration_service_123',
        isActive: true,
      };

      it('should throw ConflictException when updating to create service duplicate', async () => {
        const conflictingTemplate = {
          id: 'conflicting-template-id',
          name: 'Conflicting Template',
          serviceType: 'immigration',
          serviceId: 'different_service_456',
          isActive: true,
        };

        const updateDto: UpdateWorkflowTemplateDto = {
          serviceId: 'different_service_456', // This would create a duplicate
        };

        mockPrismaService.workflow_template.findUnique.mockResolvedValue(
          existingTemplate,
        );
        mockPrismaService.workflow_template.findFirst.mockResolvedValue(
          conflictingTemplate,
        );

        await expect(
          service.update(existingTemplate.id, updateDto, mockAdminUser),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.update(existingTemplate.id, updateDto, mockAdminUser),
        ).rejects.toThrow(
          'An active workflow template already exists for immigration/different_service_456',
        );
      });

      it('should throw ConflictException when activating template that would create duplicate', async () => {
        const inactiveTemplate = {
          ...existingTemplate,
          isActive: false,
        };

        const conflictingActiveTemplate = {
          id: 'conflicting-template-id',
          name: 'Conflicting Active Template',
          serviceType: 'immigration',
          serviceId: 'immigration_service_123',
          isActive: true,
        };

        const updateDto: UpdateWorkflowTemplateDto = {
          isActive: true, // Activating would create duplicate
        };

        mockPrismaService.workflow_template.findUnique.mockResolvedValue(
          inactiveTemplate,
        );
        mockPrismaService.workflow_template.findFirst.mockResolvedValue(
          conflictingActiveTemplate,
        );

        await expect(
          service.update(inactiveTemplate.id, updateDto, mockAdminUser),
        ).rejects.toThrow(ConflictException);
        await expect(
          service.update(inactiveTemplate.id, updateDto, mockAdminUser),
        ).rejects.toThrow(
          'An active workflow template already exists for immigration/immigration_service_123',
        );
      });

      it('should allow updating serviceType when no duplicate exists', async () => {
        const updateDto: UpdateWorkflowTemplateDto = {
          serviceType: 'training', // Different service type
        };

        const updatedTemplate = {
          ...existingTemplate,
          serviceType: 'training',
          updatedBy: 'Admin User',
        };

        mockPrismaService.workflow_template.findUnique.mockResolvedValue(
          existingTemplate,
        );
        mockPrismaService.workflow_template.findFirst.mockResolvedValue(null); // No duplicate
        mockPrismaService.workflow_template.update.mockResolvedValue(
          updatedTemplate,
        );

        const result = await service.update(
          existingTemplate.id,
          updateDto,
          mockAdminUser,
        );

        expect(result.serviceType).toBe('training');
      });

      it('should allow updating inactive template without duplicate check', async () => {
        const inactiveTemplate = {
          ...existingTemplate,
          isActive: false,
        };

        const updateDto: UpdateWorkflowTemplateDto = {
          name: 'Updated Inactive Template',
        };

        const updatedTemplate = {
          ...inactiveTemplate,
          name: 'Updated Inactive Template',
          updatedBy: 'Admin User',
        };

        mockPrismaService.workflow_template.findUnique.mockResolvedValue(
          inactiveTemplate,
        );
        mockPrismaService.workflow_template.findFirst.mockResolvedValue(null); // Name check only
        mockPrismaService.workflow_template.update.mockResolvedValue(
          updatedTemplate,
        );

        const result = await service.update(
          inactiveTemplate.id,
          updateDto,
          mockAdminUser,
        );

        expect(result.name).toBe('Updated Inactive Template');
        // Should only call findFirst once for name check, not for service duplicate check
        expect(
          mockPrismaService.workflow_template.findFirst,
        ).toHaveBeenCalledTimes(1);
      });

      it('should allow deactivating template without duplicate check', async () => {
        const updateDto: UpdateWorkflowTemplateDto = {
          isActive: false, // Deactivating
        };

        const updatedTemplate = {
          ...existingTemplate,
          isActive: false,
          updatedBy: 'Admin User',
        };

        mockPrismaService.workflow_template.findUnique.mockResolvedValue(
          existingTemplate,
        );
        mockPrismaService.workflow_template.update.mockResolvedValue(
          updatedTemplate,
        );

        const result = await service.update(
          existingTemplate.id,
          updateDto,
          mockAdminUser,
        );

        expect(result.isActive).toBe(false);
        // Should not call findFirst for service duplicate check when deactivating
        expect(
          mockPrismaService.workflow_template.findFirst,
        ).not.toHaveBeenCalled();
      });
    });
  });

  describe('findByServiceId', () => {
    const mockImmigrationService = {
      id: 'immigration_service_123',
      name: 'Standard Immigration Service',
      amount: 1000,
      service: ['visa_application', 'document_review'],
      createdAt: new Date(),
      updatedAt: new Date(),
      order: 1,
      website_visible: true,
    };

    it('should return workflow templates for valid immigration service ID', async () => {
      const serviceId = 'immigration_service_123';
      const mockTemplates = [mockWorkflowTemplate];

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockImmigrationService,
      );
      mockPrismaService.workflow_template.findMany.mockResolvedValue(
        mockTemplates,
      );

      const result = await service.findByServiceId(serviceId);

      expect(result).toHaveLength(1);
      expect(result[0].serviceId).toBe(serviceId);
      expect(result[0].serviceType).toBe('immigration');

      expect(
        mockPrismaService.immigration_service.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: serviceId },
      });

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            serviceType: 'immigration',
            serviceId: serviceId,
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
      );
    });

    it('should return workflow templates for non-immigration service ID', async () => {
      const serviceId = 'training_service_456';
      const mockTemplates = [mockWorkflowTemplate];

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(null);
      mockPrismaService.workflow_template.findMany.mockResolvedValue(
        mockTemplates,
      );

      const result = await service.findByServiceId(serviceId);

      expect(result).toHaveLength(1);

      expect(
        mockPrismaService.immigration_service.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: serviceId },
      });

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            serviceId: serviceId,
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
      );
    });

    it('should return empty array when no templates exist for service', async () => {
      const serviceId = 'immigration_service_123';

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockImmigrationService,
      );
      mockPrismaService.workflow_template.findMany.mockResolvedValue([]);

      const result = await service.findByServiceId(serviceId);

      expect(result).toHaveLength(0);
    });

    it('should handle database errors gracefully', async () => {
      const serviceId = 'immigration_service_123';
      const dbError = new Error('Database connection failed');

      mockPrismaService.immigration_service.findUnique.mockRejectedValue(
        dbError,
      );

      await expect(service.findByServiceId(serviceId)).rejects.toThrow(dbError);
    });
  });

  describe('findAll with serviceId filter', () => {
    const mockImmigrationService = {
      id: 'immigration_service_123',
      name: 'Standard Immigration Service',
      amount: 1000,
      service: ['visa_application', 'document_review'],
      createdAt: new Date(),
      updatedAt: new Date(),
      order: 1,
      website_visible: true,
    };

    it('should filter by immigration service ID when provided', async () => {
      const filters = {
        serviceId: 'immigration_service_123',
        page: 1,
        limit: 10,
      };

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockImmigrationService,
      );
      mockPrismaService.workflow_template.findMany.mockResolvedValue([
        mockWorkflowTemplate,
      ]);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      const result = await service.findAll(filters);

      expect(result.data).toHaveLength(1);
      expect(result.total).toBe(1);

      expect(
        mockPrismaService.immigration_service.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: filters.serviceId },
      });

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            serviceType: 'immigration',
            serviceId: filters.serviceId,
            isActive: undefined,
          },
          orderBy: { createdAt: 'desc' },
          skip: 0,
          take: 10,
        },
      );
    });

    it('should filter by non-immigration service ID when serviceType is not immigration', async () => {
      const filters = {
        serviceType: 'training' as any,
        serviceId: 'training_service_123',
      };

      mockPrismaService.workflow_template.findMany.mockResolvedValue([
        mockWorkflowTemplate,
      ]);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      const result = await service.findAll(filters);

      expect(result.data).toHaveLength(1);

      expect(
        mockPrismaService.immigration_service.findUnique,
      ).not.toHaveBeenCalled();

      expect(mockPrismaService.workflow_template.findMany).toHaveBeenCalledWith(
        {
          where: {
            serviceType: 'training',
            serviceId: filters.serviceId,
            isActive: undefined,
          },
          orderBy: { createdAt: 'desc' },
          skip: 0,
          take: 10,
        },
      );
    });

    it('should throw NotFoundException for non-existent immigration service ID in findAll', async () => {
      const filters = {
        serviceId: 'non_existent_id',
      };

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(null);

      await expect(service.findAll(filters)).rejects.toThrow(
        new NotFoundException(
          `Immigration service with ID ${filters.serviceId} not found`,
        ),
      );
    });

    it('should allow serviceId with immigration serviceType', async () => {
      const filters = {
        serviceType: 'immigration' as any,
        serviceId: 'immigration_service_123',
      };

      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockImmigrationService,
      );
      mockPrismaService.workflow_template.findMany.mockResolvedValue([
        mockWorkflowTemplate,
      ]);
      mockPrismaService.workflow_template.count.mockResolvedValue(1);

      const result = await service.findAll(filters);

      expect(result.data).toHaveLength(1);
      expect(
        mockPrismaService.immigration_service.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: filters.serviceId },
      });
    });
  });
});
