/**
 * Payment Data Transfer Objects (DTOs)
 *
 * This file contains all the DTOs used for payment-related API endpoints.
 * These DTOs provide input validation, API documentation, and type safety
 * for payment operations across different service types.
 *
 * Key Features:
 * - Input validation using class-validator decorators
 * - API documentation using Swagger decorators
 * - Type safety for payment operations
 * - Support for both authenticated and guest users
 *
 * DTO Hierarchy:
 * - GuestDto: Base class for guest user information
 * - Service-specific DTOs: Extend GuestDto for different service types
 * - Unified DTOs: New DTOs for unified payment API
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsInt,
  IsEmail,
  IsEnum,
  IsOptional,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  Validate,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Status } from '@prisma/client';

/**
 * Guest User Information DTO
 *
 * Base DTO containing guest user information required for non-authenticated
 * payment processing. This information is collected during checkout for
 * users who don't have an account.
 *
 * Used for:
 * - Guest payment processing
 * - Email notifications
 * - Contact information storage
 */
export class GuestDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email: string;
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  mobile_no: string;
}
export class UserMentorServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serviceId: string;
}
export class UserPackageServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  packageId: string;
}
export class UserImmigrationServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  immigration_serviceId: string;
}
export class UserTrainingServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  trainingId: string;
}

export class UserServiceDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serviceId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserPackageDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  packageId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserImmigrationDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  immigration_serviceId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}
export class UserTrainingDto extends PartialType(GuestDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  trainingId: string;

  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// ===== UNIFIED PAYMENT DTOs =====

/**
 * Service Type Enum
 * Defines the available service types for unified payments
 */
export enum ServiceType {
  SERVICE = 'service',
  PACKAGE = 'package',
  IMMIGRATION = 'immigration',
  TRAINING = 'training',
}

/**
 * Payment Type Enum
 * Defines whether the payment is from an authenticated user or guest
 */
export enum PaymentType {
  USER = 'user',
  GUEST = 'guest',
}

/**
 * Payment Method Enum
 * Defines the available payment methods for the new payment endpoint
 */
export enum PaymentMethod {
  CASH = 'cash',
  BANK_DEPOSIT = 'bank_deposit',
  ONLINE_TRANSFER = 'online_transfer',
  STRIPE = 'stripe',
}

/**
 * Custom validator for conditional transactionId requirement
 * TransactionId is required for all payment methods except stripe
 */
@ValidatorConstraint({ name: 'transactionIdRequired', async: false })
export class TransactionIdRequiredConstraint implements ValidatorConstraintInterface {
  validate(transactionId: string, args: ValidationArguments) {
    const object = args.object as any;
    const paymentMethod = object.payment_method;

    // If payment method is stripe, transactionId is optional
    if (paymentMethod === PaymentMethod.STRIPE) {
      return true;
    }

    // For all other payment methods, transactionId is required
    return transactionId && transactionId.trim().length > 0;
  }

  defaultMessage(args: ValidationArguments) {
    const object = args.object as any;
    const paymentMethod = object.payment_method;

    if (paymentMethod === PaymentMethod.STRIPE) {
      return 'Transaction ID is optional for Stripe payments';
    }

    return `Transaction ID is required for ${paymentMethod} payments`;
  }
}

/**
 * Unified Create Payment DTO
 * Single DTO for creating payments across all service types
 */
export class CreateUnifiedPaymentDto {
  @ApiProperty({
    description: 'Type of service being purchased',
    enum: ServiceType,
    example: ServiceType.SERVICE,
  })
  @IsEnum(ServiceType)
  @IsNotEmpty()
  serviceType: ServiceType;

  @ApiProperty({ description: 'ID of the service being purchased' })
  @IsString()
  @IsNotEmpty()
  serviceId: string;

  @ApiProperty({
    description: 'Type of payment (user or guest)',
    enum: PaymentType,
    example: PaymentType.USER,
  })
  @IsEnum(PaymentType)
  @IsNotEmpty()
  paymentType: PaymentType;

  @ApiProperty({ description: 'Guest full name', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Guest email address', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'Guest mobile number', required: false })
  @IsString()
  @IsOptional()
  mobile?: string;
}

/**
 * Create Multi-Method Payment DTO
 * DTO for the new /payments endpoint that supports multiple payment methods
 */
export class CreateMultiMethodPaymentDto {
  @ApiProperty({
    description: 'Total amount after discount is applied',
    example: 1000,
  })
  @IsInt()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({
    description: 'ID of the user making the payment',
    example: 'cmcp4wc4o0001istk8l2rdop7',
  })
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'Type of service being paid for',
    example: 'immigration',
  })
  @IsString()
  @IsNotEmpty()
  serviceType: string;

  @ApiProperty({
    description: 'ID of the service',
    example: 'cmcp500ap0000istocjrscl50',
  })
  @IsString()
  @IsNotEmpty()
  serviceId: string;

  @ApiProperty({
    description: 'Amount of discount applied',
    example: 200,
  })
  @IsInt()
  @IsNotEmpty()
  discount_amount: number;

  @ApiProperty({
    description: 'Original amount before discount',
    example: 1200,
  })
  @IsInt()
  @IsNotEmpty()
  actual_amount: number;

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.CASH,
  })
  @IsEnum(PaymentMethod)
  @IsNotEmpty()
  payment_method: PaymentMethod;

  @ApiProperty({
    description: 'Transaction ID - Required for cash, bank_deposit, and online_transfer. Optional for stripe.',
    example: 'INNSSND',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Validate(TransactionIdRequiredConstraint)
  transactionId?: string;

  @ApiProperty({
    description: 'Source of the payment request - determines whether to use Stripe integration',
    example: 'immigration',
    required: false,
  })
  @IsString()
  @IsOptional()
  source?: string;
}

/**
 * Multi-Method Payment Response DTO
 * Response format for the new /payments endpoint
 */
export class MultiMethodPaymentResponseDto {
  @ApiProperty({
    description: 'Payment ID',
    example: 'pay_123456789',
  })
  payment_id: string;

  @ApiProperty({
    description: 'Stripe payment link (only for stripe payments)',
    example: 'https://checkout.stripe.com/pay/cs_test_...',
    required: false,
  })
  stripe_link?: string;
}

/**
 * Guest Payment DTO
 * Strict DTO for guest payment endpoint with required fields only
 */
export class CreateGuestPaymentDto {
  @ApiProperty({
    description: 'Guest full name',
    example: 'Rohan',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Guest email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Guest mobile number',
    example: '09749155545',
  })
  @IsString()
  @IsNotEmpty()
  mobile: string;

  @ApiProperty({
    description: 'Type of service being purchased',
    enum: ServiceType,
    example: ServiceType.SERVICE,
  })
  @IsEnum(ServiceType)
  @IsNotEmpty()
  serviceType: ServiceType;

  @ApiProperty({
    description: 'ID of the service being purchased',
    example: 'p1',
  })
  @IsString()
  @IsNotEmpty()
  serviceId: string;
}

/**
 * User Payment DTO
 * Strict DTO for authenticated user payment endpoint
 */
export class CreateUserPaymentDto {
  @ApiProperty({
    description: 'Type of service being purchased',
    enum: ServiceType,
    example: ServiceType.SERVICE,
  })
  @IsEnum(ServiceType)
  @IsNotEmpty()
  serviceType: ServiceType;

  @ApiProperty({
    description: 'ID of the service being purchased',
    example: 'p1',
  })
  @IsString()
  @IsNotEmpty()
  serviceId: string;
}

/**
 * Payment Filters DTO
 * Used for filtering payment history and analytics with pagination support
 */
export class PaymentFiltersDto {
  @ApiProperty({
    description: 'Filter by service type',
    enum: ServiceType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ServiceType)
  serviceType?: ServiceType;

  @ApiProperty({
    description: 'Filter by payment type',
    enum: PaymentType,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentType)
  paymentType?: PaymentType;

  @ApiProperty({ description: 'Filter by payment status', required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ description: 'Filter by user ID', required: false })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Start date for date range filter',
    required: false,
  })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for date range filter',
    required: false,
  })
  @IsOptional()
  @IsString()
  endDate?: string;

  @ApiProperty({
    description: 'Page number for pagination (1-based)',
    required: false,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 1;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 1 : parsed;
  })
  @IsInt()
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return 10;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 10 : parsed;
  })
  @IsInt()
  limit?: number = 10;
}

/**
 * Payment Response DTO
 * Standard response format for payment operations
 */
export class PaymentResponseDto {
  @ApiProperty({ description: 'Operation status' })
  status: string;

  @ApiProperty({ description: 'Stripe checkout session URL', required: false })
  url?: string;

  @ApiProperty({ description: 'Payment ID', required: false })
  paymentId?: string;

  @ApiProperty({ description: 'Response message', required: false })
  message?: string;

  @ApiProperty({
    description: 'Stripe payment method type (card, bank_transfer, etc.)',
    required: false,
    example: 'card',
  })
  paymentMethod?: string;

  @ApiProperty({
    description: 'Stripe transaction/charge ID for reference',
    required: false,
    example: 'ch_1234567890abcdef',
  })
  transactionId?: string;

  @ApiProperty({
    description: 'Stripe PaymentIntent ID',
    required: false,
    example: 'pi_1234567890abcdef',
  })
  stripePaymentIntentId?: string;
}

/**
 * Paginated Payment History Response DTO
 * Response format for paginated payment history
 */
export class PaginatedPaymentHistoryDto {
  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Total number of items' })
  totalItems: number;

  @ApiProperty({ description: 'Array of payment records' })
  data: any[];
}

// ========================================
// ADMIN-SPECIFIC DTOs
// ========================================

/**
 * Admin Payment Progress Update DTO
 *
 * DTO for admin users to update payment progress in the unified payment table.
 * This replaces the 8 separate progress update endpoints in the admin controller.
 */
export class AdminPaymentProgressDto {
  @ApiProperty({
    description: 'Payment ID from the unified payment table',
    example: 'clx1234567890abcdef',
  })
  @IsString()
  @IsNotEmpty()
  paymentId: string;

  @ApiProperty({
    description: 'New progress status',
    enum: Status,
    enumName: 'Status',
    example: Status.Active,
  })
  @IsEnum(Status)
  progress: Status;
}

/**
 * Admin Payment Status Update DTO
 *
 * DTO for admin users to update payment status (paid, pending, failed, etc.)
 * in the unified payment table.
 */
export class AdminPaymentStatusDto {
  @ApiProperty({
    description: 'Payment ID from the unified payment table',
    example: 'clx1234567890abcdef',
  })
  @IsString()
  @IsNotEmpty()
  paymentId: string;

  @ApiProperty({
    description: 'New payment status',
    example: 'paid',
    enum: ['paid', 'pending', 'failed', 'cancelled', 'refunded'],
  })
  @IsString()
  @IsNotEmpty()
  status: string;
}

/**
 * Admin Payment Bulk Update DTO
 *
 * DTO for admin users to perform bulk updates on multiple payments.
 */
export class AdminBulkPaymentUpdateDto {
  @ApiProperty({
    description: 'Array of payment IDs to update',
    type: [String],
    example: ['clx1234567890abcdef', 'clx0987654321fedcba'],
  })
  @IsString({ each: true })
  @IsNotEmpty()
  paymentIds: string[];

  @ApiProperty({
    description: 'New progress status to apply to all payments',
    enum: Status,
    enumName: 'Status',
    example: Status.Active,
    required: false,
  })
  @IsOptional()
  @IsEnum(Status)
  progress?: Status;

  @ApiProperty({
    description: 'New payment status to apply to all payments',
    example: 'paid',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;
}
