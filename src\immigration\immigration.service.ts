import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { ImmigrationDto } from './dto/immigration.dto';

@Injectable()
export class ImmigrationService {
  constructor(private prisma: PrismaService) {}

  async create(dto: ImmigrationDto) {
    const packages = this.prisma.immigration_service.create({
      data: dto,
    });

    return packages;
  }
  async update(id: string, dto: ImmigrationDto) {
    const packages = this.prisma.immigration_service.update({
      where: {
        id,
      },
      data: dto,
    });

    return packages;
  }

  async remove(id: string) {
    const packages = this.prisma.immigration_service.delete({
      where: {
        id,
      },
    });

    return packages;
  }

  async getAll() {
    const packages = this.prisma.immigration_service.findMany({
      where: {
        website_visible: true,
      },
      orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
    });

    return packages;
  }

  async getAllForAdmin() {
    const packages = this.prisma.immigration_service.findMany({
      orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
    });

    return packages;
  }
}
