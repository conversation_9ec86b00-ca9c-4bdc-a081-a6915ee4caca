/**
 * Workflow Template Controller
 *
 * This controller provides REST API endpoints for workflow template management.
 * All endpoints require admin authentication and provide comprehensive
 * CRUD operations with proper validation and error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { WorkflowTemplateService } from './workflow-template.service';
import {
  CreateWorkflowTemplateDto,
  UpdateWorkflowTemplateDto,
  WorkflowTemplateFiltersDto,
  WorkflowTemplateResponseDto,
  PaginatedWorkflowTemplateResponseDto,
  WorkflowTemplateUsageResponseDto,
} from './dto/workflow-template.dto';
import { JwtAdmin } from '../guards/jwt.admin.guard';
import { GetUser } from '../decorator/user.decorator';
import { IJWTPayload } from '../types/auth';
import { ServiceType } from './interfaces/workflow-template.interface';

@ApiTags('Workflow Templates')
@Controller('workflow-templates')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class WorkflowTemplateController {
  private readonly logger = new Logger(WorkflowTemplateController.name);

  constructor(
    private readonly workflowTemplateService: WorkflowTemplateService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new workflow template',
    description:
      'Creates a new workflow template with the specified configuration. Admin access required.',
  })
  @ApiResponse({
    status: 201,
    description: 'Workflow template created successfully',
    type: WorkflowTemplateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or workflow template structure',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 409,
    description: 'Workflow template with this name already exists',
  })
  async create(
    @Body() createWorkflowTemplateDto: CreateWorkflowTemplateDto,
    @GetUser() user: IJWTPayload,
  ): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(
        `Admin ${user.email} creating workflow template: ${createWorkflowTemplateDto.name}`,
      );

      const result = await this.workflowTemplateService.create(
        createWorkflowTemplateDto,
        user,
      );

      this.logger.log(`Workflow template created successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all workflow templates',
    description:
      'Retrieves a paginated list of workflow templates with optional filtering. Supports filtering by service ID for specific service workflows. Admin access required.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by name or description',
    example: 'immigration',
  })
  @ApiQuery({
    name: 'serviceType',
    required: false,
    description: 'Filter by service type',
    enum: ['immigration', 'training', 'packages', 'consulting'],
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    description: 'Filter by active status',
    type: Boolean,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (1-based)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Sort field',
    enum: ['name', 'serviceType', 'createdAt', 'updatedAt'],
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order',
    enum: ['asc', 'desc'],
  })
  @ApiQuery({
    name: 'serviceId',
    required: false,
    description: 'Filter by service ID (e.g., immigration product ID when serviceType is immigration)',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow templates retrieved successfully',
    type: PaginatedWorkflowTemplateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Immigration service not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findAll(
    @Query() filters: WorkflowTemplateFiltersDto,
  ): Promise<PaginatedWorkflowTemplateResponseDto> {
    try {
      this.logger.log('Fetching workflow templates with filters', filters);

      const result = await this.workflowTemplateService.findAll(filters);

      this.logger.log(
        `Retrieved ${result.data.length} workflow templates (page ${result.page}/${result.totalPages})`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch workflow templates: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('service-type/:serviceType')
  @ApiOperation({
    summary: 'Get workflow templates by service type',
    description:
      'Retrieves all active workflow templates for a specific service type. Admin access required.',
  })
  @ApiParam({
    name: 'serviceType',
    description: 'Service type to filter by',
    enum: ['immigration', 'training', 'packages', 'consulting'],
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow templates retrieved successfully',
    type: [WorkflowTemplateResponseDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findByServiceType(
    @Param('serviceType') serviceType: ServiceType,
  ): Promise<WorkflowTemplateResponseDto[]> {
    try {
      this.logger.log(
        `Fetching workflow templates for service type: ${serviceType}`,
      );

      const result =
        await this.workflowTemplateService.findByServiceType(serviceType);

      this.logger.log(
        `Retrieved ${result.length} templates for service type: ${serviceType}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch templates by service type: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('service/:serviceId')
  @ApiOperation({
    summary: 'Get workflow templates by service ID',
    description:
      'Retrieves all active workflow templates for a specific service. Admin access required.',
  })
  @ApiParam({
    name: 'serviceId',
    description: 'Service ID to filter by',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow templates retrieved successfully',
    type: [WorkflowTemplateResponseDto],
  })
  @ApiResponse({
    status: 404,
    description: 'Service not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  async findByServiceId(
    @Param('serviceId') serviceId: string,
  ): Promise<WorkflowTemplateResponseDto[]> {
    try {
      this.logger.log(
        `Fetching workflow templates for service ID: ${serviceId}`,
      );

      const result =
        await this.workflowTemplateService.findByServiceId(serviceId);

      this.logger.log(
        `Retrieved ${result.length} templates for service ID: ${serviceId}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch templates by service ID: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a workflow template by ID',
    description:
      'Retrieves a specific workflow template by its ID. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow template retrieved successfully',
    type: WorkflowTemplateResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow template not found',
  })
  async findOne(@Param('id') id: string): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(`Fetching workflow template: ${id}`);

      const result = await this.workflowTemplateService.findOne(id);

      this.logger.log(`Workflow template retrieved successfully: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id/usage')
  @ApiOperation({
    summary: 'Check workflow template usage',
    description:
      'Checks how many applications are using this workflow template. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Usage information retrieved successfully',
    type: WorkflowTemplateUsageResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow template not found',
  })
  async checkUsage(
    @Param('id') id: string,
  ): Promise<WorkflowTemplateUsageResponseDto> {
    try {
      this.logger.log(`Checking usage for workflow template: ${id}`);

      const result = await this.workflowTemplateService.checkUsage(id);

      this.logger.log(`Usage check completed for template: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to check template usage: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update a workflow template',
    description:
      'Updates an existing workflow template with new data. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow template updated successfully',
    type: WorkflowTemplateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or workflow template structure',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow template not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Workflow template with this name already exists',
  })
  async update(
    @Param('id') id: string,
    @Body() updateWorkflowTemplateDto: UpdateWorkflowTemplateDto,
    @GetUser() user: IJWTPayload,
  ): Promise<WorkflowTemplateResponseDto> {
    try {
      this.logger.log(`Admin ${user.email} updating workflow template: ${id}`);
      console.log(id, updateWorkflowTemplateDto, user);
      const result = await this.workflowTemplateService.update(
        id,
        updateWorkflowTemplateDto,
        user,
      );

      this.logger.log(`Workflow template updated successfully: ${result.id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to update workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a workflow template',
    description:
      'Deletes a workflow template if it is not in use. Admin access required.',
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  @ApiResponse({
    status: 204,
    description: 'Workflow template deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow template not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Cannot delete workflow template - it is currently in use',
  })
  async remove(
    @Param('id') id: string,
    @GetUser() user: IJWTPayload,
  ): Promise<void> {
    try {
      this.logger.log(`Admin ${user.email} deleting workflow template: ${id}`);

      await this.workflowTemplateService.remove(id, user);

      this.logger.log(`Workflow template deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete workflow template: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
