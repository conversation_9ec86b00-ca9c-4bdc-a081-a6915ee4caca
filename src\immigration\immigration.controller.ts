import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { ImmigrationService } from './immigration.service';
import { ImmigrationDto, UpdateVisibilityDto } from './dto/immigration.dto';

@ApiTags('immigration')
@Controller('immigration')
export class ImmigrationController {
  constructor(private immigration: ImmigrationService) {}
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async create(@Body() dto: ImmigrationDto) {
    return await this.immigration.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:immigrationId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async update(
    @Param('immigrationId') id: string,
    @Body() dto: ImmigrationDto,
  ) {
    return await this.immigration.update(id, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/:immigrationId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async remove(@Param('immigrationId') id: string) {
    return await this.immigration.remove(id);
  }

  @Get('')
  async getAll() {
    return await this.immigration.getAll();
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:immigrationId/visibility')
  @ApiOperation({
    summary: '(Admin only) - Update immigration service visibility',
    description:
      'This API is restricted to admin users and allows updating the website_visible flag for a specific immigration service.',
  })
  async updateVisibility(
    @Param('immigrationId') id: string,
    @Body() dto: UpdateVisibilityDto,
  ) {
    return await this.immigration.updateVisibility(id, dto);
  }
}
