/**
 * Immigration Service Unit Tests
 *
 * Tests for the immigration service business logic including:
 * - CRUD operations
 * - Visibility filtering
 * - Database interactions
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ImmigrationService } from '../../src/immigration/immigration.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { ImmigrationDto, UpdateVisibilityDto } from '../../src/immigration/dto/immigration.dto';

describe('ImmigrationService', () => {
  let service: ImmigrationService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    immigration_service: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findMany: jest.fn(),
    },
  };

  const mockImmigrationData = {
    id: 'immigration_123',
    name: 'Work Permit Application',
    amount: 50000,
    service: ['document_review', 'application_submission', 'follow_up'],
    order: 1,
    website_visible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImmigrationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ImmigrationService>(ImmigrationService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create immigration service successfully', async () => {
      const createDto: ImmigrationDto = {
        name: 'Work Permit Application',
        amount: 50000,
        service: ['document_review', 'application_submission'],
        order: 1,
        website_visible: true,
      };

      mockPrismaService.immigration_service.create.mockResolvedValue(mockImmigrationData);

      const result = await service.create(createDto);

      expect(result).toEqual(mockImmigrationData);
      expect(mockPrismaService.immigration_service.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });
  });

  describe('update', () => {
    it('should update immigration service successfully', async () => {
      const updateDto: ImmigrationDto = {
        name: 'Updated Work Permit Application',
        amount: 55000,
        service: ['document_review', 'application_submission', 'interview_prep'],
        order: 1,
        website_visible: true,
      };

      const updatedData = { ...mockImmigrationData, ...updateDto };
      mockPrismaService.immigration_service.update.mockResolvedValue(updatedData);

      const result = await service.update('immigration_123', updateDto);

      expect(result).toEqual(updatedData);
      expect(mockPrismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id: 'immigration_123' },
        data: updateDto,
      });
    });
  });

  describe('remove', () => {
    it('should remove immigration service successfully', async () => {
      mockPrismaService.immigration_service.delete.mockResolvedValue(mockImmigrationData);

      const result = await service.remove('immigration_123');

      expect(result).toEqual(mockImmigrationData);
      expect(mockPrismaService.immigration_service.delete).toHaveBeenCalledWith({
        where: { id: 'immigration_123' },
      });
    });
  });

  describe('getAll', () => {
    it('should return only visible immigration services', async () => {
      const visibleServices = [
        mockImmigrationData,
        { ...mockImmigrationData, id: 'immigration_456', name: 'Student Visa' },
      ];

      mockPrismaService.immigration_service.findMany.mockResolvedValue(visibleServices);

      const result = await service.getAll();

      expect(result).toEqual(visibleServices);
      expect(mockPrismaService.immigration_service.findMany).toHaveBeenCalledWith({
        where: { website_visible: true },
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });

    it('should return empty array when no visible services exist', async () => {
      mockPrismaService.immigration_service.findMany.mockResolvedValue([]);

      const result = await service.getAll();

      expect(result).toEqual([]);
      expect(mockPrismaService.immigration_service.findMany).toHaveBeenCalledWith({
        where: { website_visible: true },
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
      });
    });
  });

  describe('updateVisibility', () => {
    it('should update visibility to false successfully', async () => {
      const visibilityDto: UpdateVisibilityDto = {
        website_visible: false,
      };

      const updatedData = { ...mockImmigrationData, website_visible: false };
      mockPrismaService.immigration_service.update.mockResolvedValue(updatedData);

      const result = await service.updateVisibility('immigration_123', visibilityDto);

      expect(result).toEqual(updatedData);
      expect(mockPrismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id: 'immigration_123' },
        data: { website_visible: false },
      });
    });

    it('should update visibility to true successfully', async () => {
      const visibilityDto: UpdateVisibilityDto = {
        website_visible: true,
      };

      const updatedData = { ...mockImmigrationData, website_visible: true };
      mockPrismaService.immigration_service.update.mockResolvedValue(updatedData);

      const result = await service.updateVisibility('immigration_123', visibilityDto);

      expect(result).toEqual(updatedData);
      expect(mockPrismaService.immigration_service.update).toHaveBeenCalledWith({
        where: { id: 'immigration_123' },
        data: { website_visible: true },
      });
    });
  });
});
